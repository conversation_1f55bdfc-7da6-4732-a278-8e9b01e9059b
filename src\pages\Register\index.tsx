import React, { useState } from 'react';
import { Form, Input, Button, message, Select } from 'antd';
import { useNavigate } from 'react-router-dom';
import { EyeInvisibleOutlined, EyeTwoTone } from '@ant-design/icons';
import { PhoneIcon, PasswordIcon, ArrowDownIcon, VerificationCodeIcon } from '../../components/LoginIcons';
import { sendVerificationCode, register } from '../../api/auth';

import './index.less';

interface RegisterFormValues {
  phoneNumber: string;
  verificationCode: string;
  password: string;
  confirmPassword: string;
}

type RegisterType = 'personal' | 'enterprise';

const Register: React.FC = () => {
  const [form] = Form.useForm<RegisterFormValues>();
  const navigate = useNavigate();
  const [registerType, setRegisterType] = useState<RegisterType>('personal');
  const [isLoading, setIsLoading] = useState(false);
  const [countdown, setCountdown] = useState(0);

  // 发送验证码
  const handleSendCode = async () => {
    try {
      // 先验证手机号格式
      const phoneNumber = form.getFieldValue('phoneNumber');
      if (!phoneNumber) {
        message.error('请先输入手机号');
        return;
      }

      if (!/^1[3-9]\d{9}$/.test(phoneNumber)) {
        message.error('请输入正确的手机号');
        return;
      }

      // 模拟发送验证码（开发环境）
      if (process.env.NODE_ENV === 'development') {
        // 模拟API延迟
        await new Promise(resolve => setTimeout(resolve, 1000));
        message.success('验证码已发送（开发模式：验证码为123456）');
      } else {
        // 生产环境调用真实API
        await sendVerificationCode(phoneNumber);
        message.success('验证码已发送');
      }

      // 开始倒计时
      setCountdown(60);
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } catch (error: any) {
      console.error('发送验证码失败:', error);
      message.error(error.message || '发送验证码失败');
    }
  };

  // 处理注册
  const handleRegister = async (values: RegisterFormValues) => {
    try {
      setIsLoading(true);

      // 模拟注册（开发环境）
      if (process.env.NODE_ENV === 'development') {
        // 模拟API延迟
        await new Promise(resolve => setTimeout(resolve, 1500));

        // 简单验证
        if (values.verificationCode !== '123456') {
          throw new Error('验证码错误（开发模式：请输入123456）');
        }

        console.log('注册信息:', { ...values, registerType });
        message.success('注册成功！');
      } else {
        // 生产环境调用真实API
        await register({
          ...values,
          registerType,
        });
        message.success('注册成功！');
      }

      // 注册成功后跳转到登录页面
      navigate('/platform/login');
    } catch (error: any) {
      console.error('注册失败:', error);
      message.error(error.message || '注册失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 处理注册失败
  const handleRegisterFailed = (errorInfo: any) => {
    console.log('表单验证失败:', errorInfo);
  };

  // 跳转到登录页面
  const handleGoToLogin = () => {
    navigate('/platform/login');
  };

  return (
    <div className='register-page'>
      <div className='register-container'>
        {/* 注册类型切换 */}
        <div className='register-type-tabs'>
          <div
            className={`tab-item ${registerType === 'personal' ? 'active' : ''}`}
            onClick={() => setRegisterType('personal')}
          >
            个人注册
          </div>
          <div
            className={`tab-item ${registerType === 'enterprise' ? 'active' : ''}`}
            onClick={() => setRegisterType('enterprise')}
          >
            企业注册
          </div>
        </div>

        {/* 注册表单区域 */}
        <div className='register-form-container'>
          {/* Logo和标题 */}
          <div className='header-section'>
            <div className='logo-section'>
              <svg width="79" height="68" viewBox="0 0 79 68" fill="none" xmlns="http://www.w3.org/2000/svg" className='logo-icon'>
                <path d="M13.37 0.01L79.06 67.45H13.37V0.01Z" fill="#006BFF"/>
                <path d="M0 37.48L33.15 68.12H0V37.48Z" fill="#006BFF"/>
              </svg>
            </div>
            <h1 className='platform-title'>河南省人工智能行业赋能中心（医疗）</h1>
          </div>

          <Form 
            form={form} 
            name='register' 
            onFinish={handleRegister} 
            onFinishFailed={handleRegisterFailed} 
            autoComplete='off' 
            layout='vertical'
          >
            {/* 手机号输入框 */}
            <Form.Item
              name='phoneNumber'
              rules={[
                { required: true, message: '请输入手机号' },
                { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }
              ]}
            >
              <Input
                placeholder='请输入手机号'
                prefix={<PhoneIcon />}
                addonBefore={
                  <Select
                    defaultValue='+86'
                    className='country-code-select'
                    suffixIcon={<ArrowDownIcon />}
                  >
                    <Select.Option value='+86'>+86</Select.Option>
                  </Select>
                }
                className='custom-input'
              />
            </Form.Item>

            {/* 验证码输入框 */}
            <Form.Item
              name='verificationCode'
              rules={[
                { required: true, message: '请输入验证码' },
                { len: 6, message: '验证码为6位数字' }
              ]}
            >
              <Input
                placeholder='请输入验证码'
                prefix={<VerificationCodeIcon />}
                suffix={
                  <Button
                    className='send-code-button'
                    disabled={countdown > 0}
                    onClick={handleSendCode}
                    size="small"
                  >
                    {countdown > 0 ? `${countdown}s` : '获取验证码'}
                  </Button>
                }
                className='custom-input verification-input'
              />
            </Form.Item>

            {/* 密码输入框 */}
            <Form.Item
              name='password'
              rules={[
                { required: true, message: '请设置密码' },
                { min: 6, max: 12, message: '密码长度为6-12位' }
              ]}
            >
              <Input.Password
                placeholder='请设置6~12位密码'
                prefix={<PasswordIcon />}
                iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                className='custom-input'
              />
            </Form.Item>

            {/* 确认密码输入框 */}
            <Form.Item
              name='confirmPassword'
              rules={[
                { required: true, message: '请再次输入密码' },
                { min: 6, max: 12, message: '密码长度为6-12位' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('password') === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('两次输入的密码不一致'));
                  },
                }),
              ]}
            >
              <Input.Password
                placeholder='再次输入密码'
                prefix={<PasswordIcon />}
                iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                className='custom-input'
              />
            </Form.Item>

            {/* 注册按钮 */}
            <Form.Item>
              <Button
                type='primary'
                htmlType='submit'
                loading={isLoading}
                block
                className='register-button'
              >
                注册
              </Button>
            </Form.Item>

            {/* 登录链接 */}
            <div className='login-section'>
              <span className='login-text'>已有账号？</span>
              <a onClick={handleGoToLogin} className='login-link'>立即登录</a>
            </div>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default Register;
