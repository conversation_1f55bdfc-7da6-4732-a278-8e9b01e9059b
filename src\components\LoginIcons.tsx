import React from 'react';

// 公司图标
export const CompanyIcon: React.FC = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M4 2.05V21.75H15V20.25H4V2.05Z" fill="rgba(0, 0, 0, 0.85)"/>
    <path d="M13 20V22H15V20H13Z" fill="rgba(0, 0, 0, 0.85)"/>
    <path d="M2.25 7.5H19.5V16.5H2.25V7.5Z" fill="rgba(0, 0, 0, 0.85)"/>
  </svg>
);

// 手机图标
export const PhoneIcon: React.FC = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M4.98 1.42V22.49H14V21.07H4.98V1.42Z" fill="rgba(0, 0, 0, 0.85)"/>
    <path d="M10.11 19.2H13.86V20.47H10.11V19.2Z" fill="rgba(0, 0, 0, 0.85)"/>
  </svg>
);

// 密码图标
export const PasswordIcon: React.FC = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M3.77 10.08H20.23V21.71H3.77V10.08Z" fill="rgba(0, 0, 0, 0.85)"/>
    <path d="M6.8 2.29H17.19V11.32H6.8V2.29Z" fill="rgba(0, 0, 0, 0.85)"/>
    <path d="M10.58 13.79H13.43V16.64H10.58V13.79Z" fill="rgba(0, 0, 0, 0.85)"/>
    <path d="M11.38 14.66H12.62V18.12H11.38V14.66Z" fill="rgba(0, 0, 0, 0.85)"/>
  </svg>
);

// 下拉箭头图标
export const ArrowDownIcon: React.FC = () => (
  <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M1.54 3.81L10.56 3.81L6 8.82L1.54 3.81Z" fill="#333333"/>
  </svg>
);
