.register-page {
  width: 100vw;
  height: 100vh;
  background: #EBEEF5 url(/images/login/register-background.png) no-repeat center center;
  background-size: cover;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-right: 119px;
}

.register-container {
  width: 612px;
  display: flex;
  flex-direction: column;

  // 注册类型切换标签
  .register-type-tabs {
    display: flex;
    height: 71.4px;
    margin-bottom: 0;
    position: relative;
    width: 612px;

    .tab-item {
      flex: 1;
      height: 71.4px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: 'Alibaba PuHuiTi', sans-serif;
      font-weight: 400;
      font-size: 20px;
      line-height: 1.5em;
      cursor: pointer;
      position: relative;
      background: #FFFFFF;
      color: #3377FF;
      z-index: 1;

      &:first-child {
        border-radius: 20px 0 0 0;
        background: #3377FF;
        color: #FFFFFF;
        clip-path: polygon(0 0, calc(100% - 30px) 0, 100% 100%, 0 100%);
        z-index: 2;
      }

      &:last-child {
        border-radius: 0 20px 0 0;
        margin-left: -30px;
        padding-left: 30px;

        &.active {
          background: #3377FF;
          color: #FFFFFF;
          border-radius: 0 20px 0 0;
          clip-path: polygon(30px 0, 100% 0, 100% 100%, 0 100%);
          z-index: 2;
        }

        &:not(.active) {
          border-radius: 0 20px 0 0;
          clip-path: polygon(30px 0, 100% 0, 100% 100%, 0 100%);
          z-index: 1;
        }
      }
    }
  }

  // 注册表单容器
  .register-form-container {
    background: rgba(255, 255, 255, 0.85);
    padding: 87px 119px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 40.8px;

    // 头部区域
    .header-section {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8.5px;
      width: 313.65px;

      .logo-section {
        width: 79.05px;
        height: 68.12px;
        display: flex;
        align-items: center;
        justify-content: center;


        .logo-icon {
          width: 65.69px;
          height: 67.44px;
        }
      }

      .platform-title {
        font-family: 'Alibaba PuHuiTi', sans-serif;
        font-weight: 400;
        font-size: 18px;
        line-height: 1.5em;
        color: rgba(0, 0, 0, 0.85);
        margin: 0;
        text-align: center;
      }
    }

    // 表单样式
    .ant-form {
      width: 374px;

      .ant-form-item {
        margin-bottom: 24px;

        &:last-of-type {
          margin-bottom: 0;
        }
      }

      // 自定义输入框样式
      .custom-input {
        height: 44px;
        background: #F2F3F5;
        border: none;
        border-radius: 6px;
        padding: 9px 12px;

        .ant-input {
          background: transparent;
          border: none;
          padding: 0;
          font-family: 'Alibaba PuHuiTi', sans-serif;
          font-size: 16px;
          line-height: 1.5em;
          color: rgba(0, 0, 0, 0.85);

          &::placeholder {
            color: rgba(0, 0, 0, 0.12);
          }
        }

        // 输入框前缀图标样式
        .ant-input-prefix {
          margin-right: 10px;
        }
      }

      // 手机号输入框容器
      .phone-input-wrapper {
        display: flex;
        align-items: center;
        height: 44px;
        background: #F2F3F5;
        border-radius: 6px;
        padding: 9px 12px;
        gap: 10px;

        .phone-prefix {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 24px;
          height: 24px;
        }

        .country-code-wrapper {
          display: flex;
          align-items: center;

          .country-code-select {
            width: 70px;
            background: transparent;
            border: none;

            .ant-select-selector {
              background: transparent !important;
              border: none !important;
              box-shadow: none !important;
              padding: 0 4px;
              height: auto;
            }

            .ant-select-selection-item {
              font-family: 'Alibaba PuHuiTi', sans-serif;
              font-size: 16px;
              color: rgba(0, 0, 0, 0.85);
              line-height: 1.5em;
            }
          }
        }

        .phone-input {
          flex: 1;
          background: transparent;
          border: none;
          padding: 0;
          font-family: 'Alibaba PuHuiTi', sans-serif;
          font-size: 16px;
          line-height: 1.5em;
          color: rgba(0, 0, 0, 0.85);

          &::placeholder {
            color: rgba(0, 0, 0, 0.12);
          }

          &:focus {
            box-shadow: none;
            outline: none;
          }
        }
      }

      // 验证码输入框样式
      .verification-input {
        .ant-input-suffix {
          margin-left: 10px;
        }

        .send-code-button {
          width: 100px;
          height: 26px;
          background: #66B3FF;
          border: none;
          border-radius: 100px;
          font-family: 'PingFang SC', sans-serif;
          font-weight: 300;
          font-size: 12px;
          line-height: 1.67em;
          color: #FFFFFF;
          opacity: 0.5;
          padding: 6px 8px;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            background: #66B3FF !important;
            opacity: 0.7;
          }

          &:disabled {
            background: #66B3FF !important;
            color: #FFFFFF !important;
            opacity: 0.5;
          }

          &:not(:disabled) {
            opacity: 1;
          }
        }
      }

      // 注册按钮
      .register-button {
        width: 100%;
        height: auto;
        padding: 10.2px 13.6px;
        background: #3377FF;
        border: none;
        border-radius: 5.1px;
        font-family: 'PingFang SC', sans-serif;
        font-weight: 400;
        font-size: 20px;
        color: #FFFFFF;
        margin-bottom: 17px;

        &:hover {
          background: #3377FF !important;
        }

        &:focus {
          background: #3377FF !important;
        }
      }

      // 登录区域
      .login-section {
        display: flex;
        align-items: center;
        gap: 4px;
        justify-content: center;

        .login-text {
          font-family: 'PingFang SC', sans-serif;
          font-size: 14px;
          color: rgba(0, 0, 0, 0.85);
        }

        .login-link {
          font-family: 'PingFang SC', sans-serif;
          font-weight: 300;
          font-size: 14px;
          color: #165DFF;
          text-decoration: none;
          cursor: pointer;

          &:hover {
            color: #165DFF;
            text-decoration: underline;
          }
        }
      }
    }
  }
}
