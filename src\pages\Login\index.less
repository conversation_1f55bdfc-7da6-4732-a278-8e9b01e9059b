.login-page {
  width: 100vw;
  height: 100vh;
  background: #EBEEF5 url(/images/login/register-background.png) no-repeat center center;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-right: 119px;
}

.login-container {
  width: 612px;
  display: flex;
  flex-direction: column;

  // 登录类型切换标签
  .login-type-tabs {
    display: flex;
    height: 71.4px;
    margin-bottom: 0;
    position: relative;

    .tab-item {
      width: 316.2px;
      height: 71.4px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: 'Alibaba PuHuiTi', sans-serif;
      font-weight: 400;
      font-size: 20px;
      line-height: 1.5em;
      cursor: pointer;
      position: relative;
      background: #FFFFFF;
      color: #3377FF;

      &:first-child {
        border-radius: 20px 0 0 0;

        &.active {
          background: #3377FF;
          color: #FFFFFF;
          border-radius: 20px 0 0 0;
          clip-path: polygon(0 0, calc(100% - 30px) 0, 100% 100%, 0 100%);
        }

        &:not(.active) {
          border-radius: 20px 0 0 0;
        }
      }

      &:last-child {
        border-radius: 0 20px 0 0;
        margin-left: -30px;
        padding-left: 30px;

        &.active {
          background: #3377FF;
          color: #FFFFFF;
          border-radius: 0 20px 0 0;
          clip-path: polygon(30px 0, 100% 0, 100% 100%, 0 100%);
        }

        &:not(.active) {
          border-radius: 0 20px 0 0;
          clip-path: polygon(30px 0, 100% 0, 100% 100%, 0 100%);
        }
      }
    }
  }

  // 登录表单容器
  .login-form-container {
    background: rgba(255, 255, 255, 0.85);
    padding: 87px 119px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 40.8px;

    // 头部区域
    .header-section {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8.5px;
      width: 313.65px;

      .logo-section {
        width: 79.05px;
        height: 68.12px;
        display: flex;
        align-items: center;
        justify-content: center;


        .logo-icon {
          width: 65.69px;
          height: 67.44px;
        }
      }

      .platform-title {
        font-family: 'Alibaba PuHuiTi', sans-serif;
        font-weight: 500;
        font-size: 18px;
        line-height: 1.5em;
        color: rgba(0, 0, 0, 0.85);
        margin: 0;
        text-align: center;
      }
    }

    // 登录方式切换
    .login-method-tabs {
      display: flex;
      align-items: center;
      gap: 12px;
      width: 374px;

      .method-tab {
        font-family: 'Alibaba PuHuiTi', sans-serif;
        font-size: 16px;
        line-height: 1.5em;
        cursor: pointer;
        color: rgba(0, 0, 0, 0.85);

        &.active {
          font-weight: 500;
          color: #3377FF;
        }

        &:not(.active) {
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
        }
      }

      .method-divider {
        width: 0;
        height: 18px;
        border-left: 1px solid #C9CDD4;
      }
    }

    // 表单样式
    .ant-form {
      width: 374px;

      .ant-form-item {
        margin-bottom: 24px;

        &:last-of-type {
          margin-bottom: 0;
        }
      }

      // 自定义输入框样式
      .custom-input {
        height: 44px;
        background: #F2F3F5;
        border: none;
        border-radius: 6px;
        padding: 9px 12px;

        .ant-input {
          background: transparent;
          border: none;
          padding: 0;
          font-family: 'Alibaba PuHuiTi', sans-serif;
          font-size: 16px;
          line-height: 1.5em;
          color: rgba(0, 0, 0, 0.85);

          &::placeholder {
            color: rgba(0, 0, 0, 0.12);
          }
        }

        // 输入框前缀图标样式
        .ant-input-prefix {
          margin-right: 10px;
        }
      }

      // 手机号输入框容器
      .phone-input-wrapper {
        display: flex;
        align-items: center;
        height: 44px;
        background: #F2F3F5;
        border-radius: 6px;
        padding: 9px 12px;
        gap: 10px;

        .phone-prefix {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 24px;
          height: 24px;
        }

        .country-code-wrapper {
          display: flex;
          align-items: center;

          .country-code-select {
            width: 70px;
            background: transparent;
            border: none;

            .ant-select-selector {
              background: transparent !important;
              border: none !important;
              box-shadow: none !important;
              padding: 0 4px;
              height: auto;
            }

            .ant-select-selection-item {
              font-family: 'Alibaba PuHuiTi', sans-serif;
              font-size: 16px;
              color: rgba(0, 0, 0, 0.85);
              line-height: 1.5em;
            }
          }
        }

        .phone-input {
          flex: 1;
          background: transparent;
          border: none;
          padding: 0;
          font-family: 'Alibaba PuHuiTi', sans-serif;
          font-size: 16px;
          line-height: 1.5em;
          color: rgba(0, 0, 0, 0.85);

          &::placeholder {
            color: rgba(0, 0, 0, 0.12);
          }

          &:focus {
            box-shadow: none;
            outline: none;
          }
        }
      }

      // 表单选项区域
      .form-options {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        margin-bottom: 12px;

        .remember-checkbox {
          font-family: 'PingFang SC', sans-serif;
          font-size: 14px;
          color: rgba(0, 0, 0, 0.85);

          .ant-checkbox {
            .ant-checkbox-inner {
              width: 16px;
              height: 16px;
              border: 1px solid #C9CDD4;
              border-radius: 2px;
              background: #FFFFFF;
            }
          }
        }

        .forgot-password {
          font-family: 'PingFang SC', sans-serif;
          font-size: 14px;
          color: #165DFF;
          text-decoration: none;

          &:hover {
            color: #165DFF;
            text-decoration: underline;
          }
        }
      }

      // 登录按钮
      .login-button {
        width: 100%;
        height: auto;
        padding: 10.2px 13.6px;
        background: #3377FF;
        border: none;
        border-radius: 5.1px;
        font-family: 'PingFang SC', sans-serif;
        font-weight: 500;
        font-size: 20px;
        color: #FFFFFF;
        margin-bottom: 17px;

        &:hover {
          background: #3377FF !important;
        }

        &:focus {
          background: #3377FF !important;
        }
      }

      // 注册区域
      .register-section {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;

        .register-text {
          font-family: 'PingFang SC', sans-serif;
          font-size: 14px;
          color: rgba(0, 0, 0, 0.85);
        }

        .register-link {
          font-family: 'PingFang SC', sans-serif;
          font-size: 14px;
          color: #165DFF;
          text-decoration: none;

          &:hover {
            color: #165DFF;
            text-decoration: underline;
          }
        }
      }
    }
  }

}

// 响应式调整
@media (max-width: 1440px) {
  .login-page {
    padding-right: 60px;
  }
}

@media (max-width: 1024px) {
  .login-page {
    justify-content: center;
    padding-right: 0;
  }

  .login-container {
    width: 90%;
    max-width: 612px;

    .login-form-container {
      padding: 60px 40px;
    }
  }
}

@media (max-width: 768px) {
  .login-container {
    width: 95%;

    .login-type-tabs {
      .tab-item {
        font-size: 18px;
      }
    }

    .login-form-container {
      padding: 40px 20px;
      gap: 30px;

      .header-section {
        .platform-title {
          font-size: 16px;
        }
      }

      .ant-form {
        width: 100%;
        max-width: 374px;
      }
    }
  }
}

@media (max-width: 480px) {
  .login-container {
    .login-type-tabs {
      height: 60px;

      .tab-item {
        height: 60px;
        font-size: 16px;
      }
    }

    .login-form-container {
      padding: 30px 15px;
      gap: 24px;

      .header-section {
        .logo-section {
          width: 60px;
          height: 50px;

          .logo-icon {
            width: 50px;
            height: 50px;
          }
        }

        .platform-title {
          font-size: 14px;
        }
      }
    }
  }
}
