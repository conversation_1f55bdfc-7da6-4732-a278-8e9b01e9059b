import React, { useEffect, useState } from 'react';
import { Form, Input, Button, Checkbox, message, Select } from 'antd';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useUserStore } from '../../store/userStore';
import { getToken } from '../../utils/token';
import { EyeInvisibleOutlined, EyeTwoTone } from '@ant-design/icons';
import { CompanyIcon, PhoneIcon, PasswordIcon, ArrowDownIcon } from '../../components/LoginIcons';

import './index.less';

interface LoginFormValues {
  companyName: string;
  phoneNumber: string;
  userPassword: string;
  userRemember: boolean;
}

type LoginType = 'account' | 'sms';

const Login: React.FC = () => {
  const [form] = Form.useForm<LoginFormValues>();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { login, isLoading, error } = useUserStore();
  const [loginType, setLoginType] = useState<LoginType>('account');
  const [isEnterprise, setIsEnterprise] = useState(true);

  // 从Cookie恢复记住的登录信息
  const restoreRememberedData = () => {
    const savedUsername = localStorage.getItem('remembered_username');
    const savedPassword = localStorage.getItem('remembered_password');
    if (savedUsername && savedPassword) {
      form.setFieldsValue({
        phoneNumber: savedUsername,
        userPassword: savedPassword,
        userRemember: true,
      });
    }
  };

  // 组件初始化
  useEffect(() => {
    restoreRememberedData();
  }, []);

  // 显示错误信息
  useEffect(() => {
    if (error) {
      message.error(error);
    }
  }, [error]);

  // 处理登录
  const handleLogin = async (values: LoginFormValues) => {
    try {
      // 处理记住密码
      if (values.userRemember && values.phoneNumber && values.userPassword) {
        localStorage.setItem('remembered_username', values.phoneNumber);
        localStorage.setItem('remembered_password', values.userPassword);
      } else {
        localStorage.removeItem('remembered_username');
        localStorage.removeItem('remembered_password');
      }

      // 执行登录（userStore 内部会生成 requestId）
      await login(values.phoneNumber, values.userPassword);

      message.success('登录成功！');

      // 处理重定向
      const redirectUrl = searchParams.get('redirectUrl');
      const ssoToken = getToken();

      if (redirectUrl && ssoToken) {
        console.log('SSO重定向到:', redirectUrl);
        console.log('携带token:', ssoToken);
        // SSO重定向，带上token
        window.location.href = `${redirectUrl}?ssoToken=${ssoToken}`;
      } else {
        console.log('重定向信息:', { redirectUrl, ssoToken });
        // 普通重定向
        const redirect = searchParams.get('redirect') || '/platform/';
        navigate(redirect);
      }
    } catch (error: any) {
      console.error('登录失败:', error);
    }
  };

  // 处理登录失败
  const handleLoginFailed = (errorInfo: any) => {
    console.log('表单验证失败:', errorInfo);
  };

  return (
    <div className='login-page'>
      <div className='login-container'>
        {/* 登录类型切换 */}
        <div className='login-type-tabs'>
          <div
            className={`tab-item ${!isEnterprise ? 'active' : ''}`}
            onClick={() => setIsEnterprise(false)}
          >
            个人登录
          </div>
          <div
            className={`tab-item ${isEnterprise ? 'active' : ''}`}
            onClick={() => setIsEnterprise(true)}
          >
            企业登录
          </div>
        </div>

        {/* 登录表单区域 */}
        <div className='login-form-container'>
          {/* Logo和标题 */}
          <div className='header-section'>
            <div className='logo-section'>
              <svg width="79" height="68" viewBox="0 0 79 68" fill="none" xmlns="http://www.w3.org/2000/svg" className='logo-icon'>
                <path d="M13.37 0.01L79.06 67.45H13.37V0.01Z" fill="#006BFF"/>
                <path d="M0 37.48L33.15 68.12H0V37.48Z" fill="#006BFF"/>
              </svg>
            </div>
            <h1 className='platform-title'>河南省人工智能行业赋能中心（医疗）</h1>
          </div>

          {/* 登录方式切换 */}
          <div className='login-method-tabs'>
            <div
              className={`method-tab ${loginType === 'account' ? 'active' : ''}`}
              onClick={() => setLoginType('account')}
            >
              账号密码登录
            </div>
            <div className='method-divider'></div>
            <div
              className={`method-tab ${loginType === 'sms' ? 'active' : ''}`}
              onClick={() => setLoginType('sms')}
            >
              验证码登录
            </div>
          </div>

          <Form form={form} name='login' onFinish={handleLogin} onFinishFailed={handleLoginFailed} autoComplete='off' layout='vertical'>
            {/* 公司名称输入框 */}
            <Form.Item
              name='companyName'
              rules={[{ required: true, message: '请输入公司名称' }]}
            >
              <Input
                placeholder='公司名称'
                prefix={<CompanyIcon />}
                className='custom-input'
              />
            </Form.Item>

            {/* 手机号输入框 */}
            <Form.Item
              name='phoneNumber'
              rules={[
                { required: true, message: '请输入手机号' },
                { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }
              ]}
            >
              <Input
                placeholder='请输入手机号'
                prefix={<PhoneIcon />}
                addonBefore={
                  <Select
                    defaultValue='+86'
                    className='country-code-select'
                    suffixIcon={<ArrowDownIcon />}
                  >
                    <Select.Option value='+86'>+86</Select.Option>
                  </Select>
                }
                className='custom-input'
              />
            </Form.Item>

            {/* 密码输入框 */}
            <Form.Item
              name='userPassword'
              rules={[
                { required: true, message: '请输入密码' },
                { min: 6, max: 12, message: '密码长度为6-12位' }
              ]}
            >
              <Input.Password
                placeholder='请输入6~12位密码'
                prefix={<PasswordIcon />}
                iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                className='custom-input'
              />
            </Form.Item>

            {/* 记住密码和忘记密码 */}
            <div className='form-options'>
              <Form.Item name='userRemember' valuePropName='checked' initialValue={false} noStyle>
                <Checkbox className='remember-checkbox'>记住密码</Checkbox>
              </Form.Item>
              <a href='#' className='forgot-password'>忘记密码?</a>
            </div>

            {/* 登录按钮 */}
            <Form.Item>
              <Button
                type='primary'
                htmlType='submit'
                loading={isLoading}
                block
                className='login-button'
              >
                登 录
              </Button>
            </Form.Item>

            {/* 注册链接 */}
            <div className='register-section'>
              <span className='register-text'>没有账户？</span>
              <a href='#' className='register-link'>立即注册</a>
            </div>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default Login;
