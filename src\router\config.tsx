import type { RouteConfig } from './types';
import { FullScreenLayout, MainLayout } from '../layouts';
import HomePage from '../pages/Home';
import ProductPage from '../pages/Product';
import PricingPage from '../pages/Pricing';
import ScientificResearchPage from '../pages/ScientificResearch';
import ResearchOperationsPage from '../pages/ResearchOperations';
import LoginPage from '../pages/Login';
import PrivacyPage from '../pages/Privacy';
import TermsPage from '../pages/Terms';
import ComputingMall from '../pages/ComputingMall';
import ModelMall from '../pages/ModelMall';
import AppMall from '../pages/AppMall';
import AppDetail from '../pages/AppMall/Detail';
import DataManagement from '../pages/DataManagement';
import SecurityDashboard from '../pages/SecurityDashboard';
import SecurityOps from '../pages/SecurityOps';

import About from '../pages/About';
import Management from '../pages/Management';
import Dashboard from '../pages/Dashboard';
import NotFound from '../pages/ErrorPages/404';
import Forbidden from '../pages/ErrorPages/403';

// 产学研运营子页面
import CollaborationPage from '../pages/ResearchOperations/CollaborationPage';
import ResourcesSharingPage from '../pages/ResearchOperations/ResourcesSharingPage';
import ProjectsCoursesPage from '../pages/ResearchOperations/ProjectsCoursesPage';
import TalentDevelopmentPage from '../pages/ResearchOperations/TalentDevelopmentPage';
import AchievementTransferPage from '../pages/ResearchOperations/AchievementTransferPage';
import CommunicationCenterPage from '../pages/ResearchOperations/CommunicationCenterPage';
import SecurityCenterPage from '../pages/ResearchOperations/SecurityCenterPage';

// 科研赋能子页面
import UserManual from '../pages/ScientificResearch/UserManual';
import TechnicalGuide from '../pages/ScientificResearch/TechnicalGuide';
import DataOperation from '@/pages/DataOperation';
import NewsCenter from '@/pages/NewsCenter';
import NoticeAnnouncement from '@/pages/NewsCenter/NoticeAnnouncement';
import PolicyFile from '@/pages/NewsCenter/PolicyFile';
import IndustryInformation from '@/pages/NewsCenter/IndustryInformation';

// 路由配置 - 使用嵌套路由和Layout
export const routes: RouteConfig[] = [
  // 安全平台路由 - 使用FullScreenLayout (全屏，无头部底部)
  {
    path: '/platform/security-dashboard',
    component: () => <FullScreenLayout />,
    children: [
      {
        path: '',
        component: () => <SecurityDashboard />,
        meta: {
          title: '安全赋能一体化运营平台',
          requiresAuth: false,
        },
      },
    ],
  },
  // 主应用路由 - 使用MainLayout
  {
    path: '/platform',
    component: () => <MainLayout />,
    children: [
      {
        path: '',
        component: () => <HomePage />,
        meta: {
          title: '首页',
          requiresAuth: false,
          icon: 'home',
        },
      },
      {
        path: 'product',
        component: () => <ProductPage />,
        meta: {
          title: '产品',
          requiresAuth: false,
          icon: 'appstore',
        },
      },
      {
        path: 'pricing',
        component: () => <PricingPage />,
        meta: {
          title: '价格',
          requiresAuth: false,
          icon: 'dollar',
        },
      },
      {
        path: 'computing-mall',
        component: () => <ComputingMall />,
        meta: {
          title: '算力商城',
          requiresAuth: false,
          icon: 'cloud',
        },
      },
      {
        path: 'model-mall',
        component: () => <ModelMall />,
        meta: {
          title: '模型商城',
          requiresAuth: false,
          icon: 'database',
        },
      },
      {
        path: 'app-mall',
        component: () => <AppMall />,
        meta: {
          title: '应用商城',
          requiresAuth: false,
          icon: 'appstore',
        },
      },
      {
        path: 'app-mall/:id',
        component: () => <AppDetail />,
        meta: {
          title: '应用详情',
          requiresAuth: false,
          hidden: true, // 不在菜单中显示
        },
      },
      {
        path: 'data-management',
        component: () => <DataManagement />,
        meta: {
          title: '数据空间',
          requiresAuth: false,
          icon: 'database',
        },
      },
      {
        path: 'security-ops',
        component: () => <SecurityOps />,
        meta: {
          title: '安全运营',
          requiresAuth: false,
          icon: 'safety',
        },
      },
      {
        path: 'scientific-research',
        component: () => <ScientificResearchPage />,
        meta: {
          title: '产学研赋能',
          requiresAuth: false,
          icon: 'experiment',
        },
      },
      {
        path: 'scientific-research/product',
        component: () => <ProductPage />,
        meta: {
          title: '科研产品',
          requiresAuth: false,
          hidden: true, // 不在主导航中显示
        },
      },
      {
        path: 'scientific-research/pricing',
        component: () => <PricingPage />,
        meta: {
          title: '科研定价',
          requiresAuth: false,
          hidden: true, // 不在主导航中显示
        },
      },
      {
        path: 'scientific-research/about',
        component: () => <About />,
        meta: {
          title: '关于科研平台',
          requiresAuth: false,
          hidden: true, // 不在主导航中显示
        },
      },
      {
        path: 'scientific-research/user-manual',
        component: () => <UserManual />,
        meta: {
          title: '使用手册',
          requiresAuth: false,
          hidden: true, // 不在主导航中显示
        },
      },
      {
        path: 'scientific-research/technical-guide',
        component: () => <TechnicalGuide />,
        meta: {
          title: '技术指南',
          requiresAuth: false,
          hidden: true, // 不在主导航中显示
        },
      },
      {
        path: 'research-operations',
        component: () => <ResearchOperationsPage />,
        meta: {
          title: '产学研运营',
          requiresAuth: false,
          icon: 'team',
        },
      },
      // 产学研运营子路由
      {
        path: 'research-operations/collaboration',
        component: () => <CollaborationPage />,
        meta: {
          title: '多组织协作',
          requiresAuth: false,
          hidden: true,
        },
      },
      {
        path: 'research-operations/resources-sharing',
        component: () => <ResourcesSharingPage />,
        meta: {
          title: '资源目录与共享',
          requiresAuth: false,
          hidden: true,
        },
      },
      {
        path: 'research-operations/projects-courses',
        component: () => <ProjectsCoursesPage />,
        meta: {
          title: '项目与课程',
          requiresAuth: false,
          hidden: true,
        },
      },
      {
        path: 'research-operations/talent-development',
        component: () => <TalentDevelopmentPage />,
        meta: {
          title: '人才培养',
          requiresAuth: false,
          hidden: true,
        },
      },
      {
        path: 'research-operations/achievement-transfer',
        component: () => <AchievementTransferPage />,
        meta: {
          title: '成果转化与技术转移',
          requiresAuth: false,
          hidden: true,
        },
      },
      {
        path: 'research-operations/communication-center',
        component: () => <CommunicationCenterPage />,
        meta: {
          title: '交流中心',
          requiresAuth: false,
          hidden: true,
        },
      },
      {
        path: 'research-operations/security-center',
        component: () => <SecurityCenterPage />,
        meta: {
          title: '安全中心',
          requiresAuth: false,
          hidden: true,
        },
      },
      {
        path: 'data-operation',
        component: () => <DataOperation />,
        meta: {
          title: '数据运营',
          requiresAuth: false,
          hidden: true,
        },
      },

      {
        path: 'about',
        component: () => <About />,
        meta: {
          title: '关于我们',
          requiresAuth: false,
          icon: 'info',
        },
      },
      {
        path: 'management',
        component: () => <Management />,
        meta: {
          title: '管理中心',
          requiresAuth: false, // 管理中心需要登录
          hidden: true,
        },
        children: [
          {
            path: 'dashboard',
            component: () => <Dashboard />,
            meta: {
              title: '算力资源调度看板',
              requiresAuth: false,
              icon: 'dashboard',
            },
          },
        ],
      },
      {
        path: 'news-center',
        component: () => <NewsCenter />,
        meta: {
          title: '中心动态',
          requiresAuth: false,
          icon: 'news',
        },
        children: [
          {
            path: 'notice-announcement',
            component: () => <NoticeAnnouncement />,
            meta: {
              title: '通知公告',
              requiresAuth: false,
            },
          },
          {
            path: 'policy-file',
            component: () => <PolicyFile />,
            meta: {
              title: '政策文件',
              requiresAuth: false,
            },
          },
          {
            path: 'industry-information',
            component: () => <IndustryInformation />,
            meta: {
              title: '行业资讯',
              requiresAuth: false,
            },
          },
        ],
      },
      // 法律页面
      {
        path: 'privacy',
        component: () => <PrivacyPage />,
        meta: {
          title: '隐私政策',
          requiresAuth: false,
          hidden: true, // 不在导航中显示
        },
      },
      {
        path: 'terms',
        component: () => <TermsPage />,
        meta: {
          title: '用户协议',
          requiresAuth: false,
          hidden: true, // 不在导航中显示
        },
      },
      // 错误页面
      {
        path: '403',
        component: () => <Forbidden />,
        meta: {
          title: '权限不足',
          requiresAuth: false,
          hidden: true,
        },
      },
      {
        path: '*',
        component: () => <NotFound />,
        meta: {
          title: '页面未找到',
          requiresAuth: false,
          hidden: true,
        },
      },
    ],
  },
  // 为了兼容性保留 /platform/login 路由，重定向到 /auth/login
  {
    path: '/platform/login',
    component: () => <LoginPage />,
    meta: {
      title: '用户登录',
      requiresAuth: false,
      hidden: true,
    },
  },
  // 根路径重定向到 /platform
  {
    path: '/',
    component: () => {
      window.location.replace('/platform');
      return null;
    },
    meta: {
      title: '重定向到平台',
      requiresAuth: false,
      hidden: true,
    },
  },
];

// 获取导航菜单项
export const getNavigationItems = (): any[] => {
  const flattenRoutes = (routes: RouteConfig[], parentPath = ''): any[] => {
    const items: any[] = [];

    routes.forEach(route => {
      // 如果有子路由，递归处理
      if (route.children) {
        items.push(...flattenRoutes(route.children, route.path === '/' ? '' : route.path));
      } else if (!route.meta?.hidden && route.meta?.icon) {
        // 构建完整路径
        const fullPath = parentPath ? `${parentPath}/${route.path}` : route.path;
        items.push({
          key: fullPath,
          path: fullPath.startsWith('/') ? fullPath : `/${fullPath}`,
          title: route.meta?.title || '',
          icon: route.meta?.icon,
          requiresAuth: route.meta?.requiresAuth,
          roles: route.meta?.roles,
        });
      }
    });

    return items;
  };

  return flattenRoutes(routes);
};

// 根据路径查找路由配置
export const findRouteByPath = (path: string): RouteConfig | undefined => {
  const searchInRoutes = (
    routes: RouteConfig[],
    targetPath: string,
    parentPath = ''
  ): RouteConfig | undefined => {
    for (const route of routes) {
      // 构建完整路径，处理嵌套路由的路径拼接
      let fullPath: string;
      if (parentPath === '' && route.path === '/') {
        fullPath = '/';
      } else if (parentPath === '/' || parentPath === '') {
        fullPath = route.path === '' ? '/' : `/${route.path}`;
      } else {
        fullPath = `${parentPath}/${route.path}`;
      }

      // 清理重复的斜杠
      fullPath = fullPath.replace(/\/+/g, '/');

      // 精确匹配
      if (fullPath === targetPath) {
        return route;
      }

      // 处理动态路由
      if (fullPath.includes(':')) {
        const routePattern = fullPath.replace(/:[^/]+/g, '[^/]+');
        const regex = new RegExp(`^${routePattern}$`);
        if (regex.test(targetPath)) {
          return route;
        }
      }

      // 递归搜索子路由
      if (route.children) {
        const childRoute = searchInRoutes(
          route.children,
          targetPath,
          fullPath === '/' ? '' : fullPath
        );
        if (childRoute) {
          return childRoute;
        }
      }
    }

    return undefined;
  };

  return searchInRoutes(routes, path);
};

// 获取页面标题
export const getPageTitle = (path: string): string => {
  // 首先尝试从路由配置获取标题
  const route = findRouteByPath(path);
  if (route?.meta?.title) {
    return route.meta.title;
  }

  // 如果路由配置中没有，尝试从菜单配置中获取
  try {
    import('../config/menuConfig').then(({ getMenuConfigByPath }) => {
      const menuConfig = getMenuConfigByPath(path);

      // 在菜单项中查找匹配的路径
      const findMenuTitle = (items: any[], targetPath: string): string | null => {
        for (const item of items) {
          if (item.path === targetPath) {
            return item.label;
          }
          if (item.children) {
            const childTitle = findMenuTitle(item.children, targetPath);
            if (childTitle) {
              return childTitle;
            }
          }
        }
        return null;
      };

      const menuTitle = findMenuTitle(menuConfig.items, path);
      if (menuTitle) {
        return menuTitle;
      }
    });
  } catch (error) {
    console.warn('无法从菜单配置获取标题:', error);
  }

  // 最后尝试根据路径推断标题
  const pathSegments = path.split('/').filter(segment => segment);
  if (pathSegments.length === 0) {
    return '首页';
  }

  const pathToTitleMap: Record<string, string> = {
    platform: '首页',
    'computing-mall': '算力商城',
    'model-mall': '模型商城',
    'app-mall': '应用商城',
    'data-management': '数据空间',
    'security-ops': '安全运营',
    'scientific-research': '产学研赋能',
    'research-operations': '产学研运营',
    about: '关于我们',
    pricing: '价格',
    product: '产品',
    management: '管理中心',
    dashboard: '算力资源调度看板',
    login: '用户登录',
    privacy: '隐私政策',
    terms: '用户协议',
    'security-dashboard': '安全赋能一体化运营平台',
  };

  const lastSegment = pathSegments[pathSegments.length - 1];
  return pathToTitleMap[lastSegment] || '未知页面';
};
